package de.sarocesch;

import de.sarocesch.launcher.LauncherApp;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

public class Main {
    public static void main(String[] args) {
        System.out.println("Minewache-Client wird gestartet...");

        // Optimierte System-Properties für JavaFX (jpackage benötigt weniger Konfiguration)
        System.setProperty("prism.order", "d3d,sw");
        System.setProperty("prism.verbose", "false");
        System.setProperty("javafx.animation.fullspeed", "true");

        // Starte die Launcher-Anwendung
        try {
            System.out.println("Starte JavaFX Launcher-Anwendung...");
            System.out.println("Java Version: " + System.getProperty("java.version"));
            System.out.println("JavaFX Runtime Version: " + System.getProperty("javafx.runtime.version"));
            System.out.println("OS: " + System.getProperty("os.name"));

            // Prüfe ob JavaFX verfügbar ist
            try {
                Class.forName("javafx.application.Application");
                System.out.println("JavaFX Application class gefunden");
            } catch (ClassNotFoundException e) {
                System.err.println("JavaFX Application class nicht gefunden!");
                throw e;
            }

            LauncherApp.launch();
        } catch (Exception e) {
            System.err.println("Kritischer Fehler beim Starten der Launcher-Anwendung: " + e.getMessage());
            e.printStackTrace();

            // Warte 5 Sekunden bevor das Programm beendet wird
            try {
                Thread.sleep(5000);
            } catch (InterruptedException ie) {
                Thread.currentThread().interrupt();
            }
            System.exit(1);
        }
    }
}