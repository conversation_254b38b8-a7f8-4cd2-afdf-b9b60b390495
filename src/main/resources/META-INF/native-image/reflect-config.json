[{"name": "com.sun.javafx.tk.quantum.QuantumToolkit", "methods": [{"name": "<init>", "parameterTypes": []}], "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "com.sun.javafx.tk.quantum.QuantumRenderer", "methods": [{"name": "<init>", "parameterTypes": []}], "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "com.sun.glass.ui.win.WinPlatformFactory", "methods": [{"name": "<init>", "parameterTypes": []}], "allDeclaredConstructors": true, "allPublicConstructors": true}, {"name": "com.sun.glass.ui.win.WinApplication", "methods": [{"name": "<init>", "parameterTypes": []}], "allDeclaredConstructors": true, "allPublicConstructors": true}, {"name": "com.sun.glass.ui.win.WinGestureSupport", "allDeclaredConstructors": true, "allPublicConstructors": true}, {"name": "com.sun.glass.ui.win.WinDnDClipboard", "allDeclaredConstructors": true, "allPublicConstructors": true}, {"name": "com.sun.prism.d3d.D3DPipeline", "allDeclaredConstructors": true, "allPublicConstructors": true}, {"name": "com.sun.prism.d3d.D3DResourceFactory", "allDeclaredConstructors": true, "allPublicConstructors": true}, {"name": "com.sun.prism.sw.SWPipeline", "methods": [{"name": "getInstance", "parameterTypes": []}, {"name": "isSupported", "parameterTypes": []}, {"name": "createResourceFactory", "parameterTypes": ["com.sun.glass.ui.Screen[]", "com.sun.prism.ResourceFactoryListener"]}], "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true, "allDeclaredFields": true, "allPublicFields": true}, {"name": "javafx.stage.Stage", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "javafx.scene.Scene", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "javafx.application.Application", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true, "allDeclaredFields": true, "allPublicFields": true}, {"name": "javafx.application.Platform", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true, "allDeclaredFields": true, "allPublicFields": true}, {"name": "com.sun.prism.sw.SWResourceFactory", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "com.sun.prism.sw.SWGraphics", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "com.sun.glass.ui.win.WinWindow", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "com.sun.glass.ui.win.WinView", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "com.sun.glass.ui.win.WinScreen", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "de.sarocesch.Main", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true, "allDeclaredFields": true, "allPublicFields": true}, {"name": "de.sarocesch.launcher.LauncherApp", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true, "allDeclaredFields": true, "allPublicFields": true}]