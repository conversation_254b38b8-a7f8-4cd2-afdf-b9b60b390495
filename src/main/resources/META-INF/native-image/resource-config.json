{"resources": {"includes": [{"pattern": ".*\\.css$"}, {"pattern": ".*\\.fxml$"}, {"pattern": ".*\\.properties$"}, {"pattern": ".*\\.png$"}, {"pattern": ".*\\.jpg$"}, {"pattern": ".*\\.gif$"}, {"pattern": ".*\\.ttf$"}, {"pattern": ".*\\.otf$"}, {"pattern": "META-INF/services/.*"}, {"pattern": ".*prism_.*\\.dll$"}, {"pattern": ".*glass\\.dll$"}, {"pattern": ".*javafx_.*\\.dll$"}, {"pattern": ".*jfxwebkit\\.dll$"}, {"pattern": ".*\\.so$"}, {"pattern": ".*\\.dylib$"}, {"pattern": ".*\\.dll$"}, {"pattern": ".*api-ms-win-.*\\.dll$"}, {"pattern": ".*vcruntime.*\\.dll$"}, {"pattern": ".*msvcp.*\\.dll$"}, {"pattern": ".*ucrtbase.*\\.dll$"}, {"pattern": ".*gstreamer.*"}, {"pattern": ".*fxplugins.*"}, {"pattern": "com/sun/javafx/scene/control/skin/resources/.*"}, {"pattern": "com/sun/javafx/tk/quantum/.*"}]}, "bundles": [{"name": "com.sun.javafx.scene.control.skin.resources.controls"}, {"name": "com.sun.javafx.tk.quantum.QuantumMessagesBundle"}]}