[{"name": "com.sun.glass.ui.win.WinApplication", "methods": [{"name": "_initIDs", "parameterTypes": []}, {"name": "_createWindow", "parameterTypes": ["long", "long", "int", "int", "int", "int", "int", "int", "int", "int", "int", "int", "int", "float"]}, {"name": "_getScreens", "parameterTypes": []}]}, {"name": "com.sun.glass.ui.Screen", "methods": [{"name": "_initIDs", "parameterTypes": []}]}, {"name": "com.sun.prism.d3d.D3DPipeline", "methods": [{"name": "nGetAdapterOrdinal", "parameterTypes": ["long"]}, {"name": "nGetMaxSampleSupport", "parameterTypes": []}, {"name": "nIsD3DAvailable", "parameterTypes": []}]}, {"name": "com.sun.glass.ui.win.WinWindow", "methods": [{"name": "_initIDs", "parameterTypes": []}, {"name": "_close", "parameterTypes": ["long"]}, {"name": "_setVisible", "parameterTypes": ["long", "boolean"]}, {"name": "_minimize", "parameterTypes": ["long", "boolean"]}, {"name": "_maximize", "parameterTypes": ["long", "boolean", "boolean"]}]}, {"name": "com.sun.glass.ui.win.WinView", "methods": [{"name": "_initIDs", "parameterTypes": []}, {"name": "_create", "parameterTypes": ["java.util.Map"]}, {"name": "_getN<PERSON><PERSON><PERSON><PERSON>", "parameterTypes": ["long"]}, {"name": "_setParent", "parameterTypes": ["long", "long"]}]}, {"name": "com.sun.prism.sw.SWPipeline", "methods": [{"name": "isSupported", "parameterTypes": []}, {"name": "getInstance", "parameterTypes": []}, {"name": "createResourceFactory", "parameterTypes": ["com.sun.glass.ui.Screen[]", "com.sun.prism.ResourceFactoryListener"]}]}, {"name": "com.sun.prism.d3d.D3DPipeline", "methods": [{"name": "isSupported", "parameterTypes": []}, {"name": "getInstance", "parameterTypes": []}, {"name": "createResourceFactory", "parameterTypes": ["com.sun.glass.ui.Screen[]", "com.sun.prism.ResourceFactoryListener"]}, {"name": "nGetAdapterOrdinal", "parameterTypes": ["long"]}, {"name": "nGetMaxSampleSupport", "parameterTypes": []}, {"name": "nIsD3DAvailable", "parameterTypes": []}]}, {"name": "com.sun.prism.sw.SWContext", "methods": [{"name": "initNativeCtxInfo", "parameterTypes": []}]}]