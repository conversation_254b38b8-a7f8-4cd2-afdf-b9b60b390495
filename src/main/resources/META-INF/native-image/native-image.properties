# Native Image Configuration for JavaFX Minewache-Client
Args = --no-fallback \
       --enable-http \
       --enable-https \
       --enable-url-protocols=http,https,file \
       --report-unsupported-elements-at-runtime \
       -H:+ReportExceptionStackTraces \
       --allow-incomplete-classpath \
       -H:+UnlockExperimentalVMOptions \
       --add-modules=javafx.controls,javafx.fxml,javafx.web,javafx.graphics,javafx.base \
       --initialize-at-run-time=javafx.scene.image.Image \
       --initialize-at-run-time=javafx.scene.media \
       --initialize-at-run-time=javafx.scene.web \
       --initialize-at-run-time=javafx.application.Platform \
       --initialize-at-run-time=com.sun.javafx.tk.Toolkit \
       --initialize-at-run-time=com.sun.javafx \
       --initialize-at-run-time=com.sun.glass \
       --initialize-at-run-time=com.sun.prism \
       --initialize-at-run-time=com.sun.scenario \
       --initialize-at-run-time=com.sun.webkit \
       --initialize-at-run-time=com.sun.prism.impl.PrismSettings \
       --initialize-at-run-time=com.sun.prism.GraphicsPipeline \
       -H:+AddAllCharsets \
       -H:IncludeResourceBundles=com.sun.javafx.scene.control.skin.resources.controls \
       -H:+AllowIncompleteClasspath \
       -H:+ReportUnsupportedElementsAtRuntime \
       -H:+JNI \
       -H:+AllowVMInspection \
       --add-opens=javafx.graphics/com.sun.javafx.tk=ALL-UNNAMED \
       --add-opens=javafx.graphics/com.sun.javafx.tk.quantum=ALL-UNNAMED \
       --add-opens=javafx.graphics/com.sun.glass.ui=ALL-UNNAMED \
       --add-opens=javafx.graphics/com.sun.glass.ui.win=ALL-UNNAMED \
       --add-opens=javafx.graphics/com.sun.javafx.application=ALL-UNNAMED \
       --add-opens=javafx.graphics/com.sun.prism=ALL-UNNAMED \
       --add-opens=javafx.graphics/com.sun.prism.d3d=ALL-UNNAMED \
       --add-opens=javafx.graphics/com.sun.prism.sw=ALL-UNNAMED \
       --add-opens=javafx.graphics/com.sun.javafx.scene=ALL-UNNAMED \
       --add-opens=javafx.graphics/com.sun.javafx.sg.prism=ALL-UNNAMED \
       --add-opens=javafx.graphics/com.sun.scenario.effect=ALL-UNNAMED \
       --add-opens=javafx.graphics/com.sun.scenario.effect.impl.prism=ALL-UNNAMED \
       --add-opens=javafx.graphics/com.sun.scenario.effect.impl.prism.ps=ALL-UNNAMED \
       --add-opens=javafx.graphics/com.sun.scenario.effect.impl.prism.sw=ALL-UNNAMED \
       --add-opens=javafx.controls/com.sun.javafx.scene.control=ALL-UNNAMED \
       --add-opens=javafx.controls/com.sun.javafx.scene.control.behavior=ALL-UNNAMED \
       --add-opens=javafx.controls/com.sun.javafx.scene.control.skin=ALL-UNNAMED \
       --add-opens=javafx.base/com.sun.javafx.runtime=ALL-UNNAMED \
       --add-opens=javafx.base/com.sun.javafx.collections=ALL-UNNAMED \
       --add-opens=java.base/java.lang.reflect=ALL-UNNAMED \
       --add-opens=java.base/java.lang=ALL-UNNAMED
