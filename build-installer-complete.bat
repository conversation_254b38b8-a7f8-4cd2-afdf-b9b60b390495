@echo off
echo ========================================
echo Minewache-Client Complete Installer Build
echo ========================================

echo.
echo Dieses Script erstellt einen vollstaendigen Windows-Installer
echo fuer den Minewache-Client mit Inno Setup.
echo.

:: Prüfe ob Inno Setup installiert ist
set "INNO_SETUP_PATH="
if exist "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" (
    set "INNO_SETUP_PATH=C:\Program Files (x86)\Inno Setup 6\ISCC.exe"
) else if exist "C:\Program Files\Inno Setup 6\ISCC.exe" (
    set "INNO_SETUP_PATH=C:\Program Files\Inno Setup 6\ISCC.exe"
) else if exist "C:\Program Files (x86)\Inno Setup 5\ISCC.exe" (
    set "INNO_SETUP_PATH=C:\Program Files (x86)\Inno Setup 5\ISCC.exe"
) else if exist "C:\Program Files\Inno Setup 5\ISCC.exe" (
    set "INNO_SETUP_PATH=C:\Program Files\Inno Setup 5\ISCC.exe"
)

if "%INNO_SETUP_PATH%"=="" (
    echo.
    echo FEHLER: Inno Setup nicht gefunden!
    echo.
    echo Bitte installieren Sie Inno Setup von:
    echo https://jrsoftware.org/isinfo.php
    echo.
    echo Unterstuetzte Versionen: Inno Setup 5 oder 6
    echo.
    pause
    exit /b 1
)

echo Inno Setup gefunden: %INNO_SETUP_PATH%
echo.

:: Schritt 1: Projekt bereinigen
echo Schritt 1: Projekt bereinigen...
call gradlew clean
if %ERRORLEVEL% neq 0 (
    echo FEHLER: Clean fehlgeschlagen!
    pause
    exit /b 1
)

:: Schritt 2: Shadow JAR erstellen
echo.
echo Schritt 2: Shadow JAR erstellen...
call gradlew shadowJar
if %ERRORLEVEL% neq 0 (
    echo FEHLER: Shadow JAR Build fehlgeschlagen!
    pause
    exit /b 1
)

:: Schritt 3: jpackage App-Image erstellen
echo.
echo Schritt 3: jpackage App-Image erstellen...
call gradlew jpackage
if %ERRORLEVEL% neq 0 (
    echo FEHLER: jpackage Build fehlgeschlagen!
    pause
    exit /b 1
)

:: Schritt 4: Inno Setup Installer erstellen
echo.
echo Schritt 4: Inno Setup Installer erstellen...
call gradlew createInstaller
if %ERRORLEVEL% neq 0 (
    echo FEHLER: Installer-Erstellung fehlgeschlagen!
    pause
    exit /b 1
)

echo.
echo ========================================
echo BUILD ERFOLGREICH ABGESCHLOSSEN!
echo ========================================
echo.

:: Prüfe Ergebnisse
if exist "build\jpackage\MinewacheClient\MinewacheClient.exe" (
    echo [OK] App-Image erstellt: build\jpackage\MinewacheClient\MinewacheClient.exe
) else (
    echo [WARNUNG] App-Image nicht gefunden!
)

if exist "build\installer\MinewacheClient-Installer-1.0.0.exe" (
    echo [OK] Installer erstellt: build\installer\MinewacheClient-Installer-1.0.0.exe
    
    :: Dateigröße anzeigen
    for %%A in ("build\installer\MinewacheClient-Installer-1.0.0.exe") do (
        set /a "size=%%~zA / 1024 / 1024"
        echo [INFO] Installer-Groesse: !size! MB
    )
) else (
    echo [WARNUNG] Installer nicht gefunden!
)

echo.
echo Icon-Status pruefen:
if exist "src\main\resources\icons\logo.ico" (
    echo [OK] Icon-Datei gefunden: src\main\resources\icons\logo.ico
) else (
    echo [WARNUNG] Icon-Datei nicht gefunden!
    echo Erstellen Sie die Datei: src\main\resources\icons\logo.ico
)

echo.
echo ========================================
echo VERTEILUNG
echo ========================================
echo.
echo Fuer die Verteilung benoetigen Sie nur diese eine Datei:
echo build\installer\MinewacheClient-Installer-1.0.0.exe
echo.
echo Diese Datei enthaelt:
echo - Den kompletten Minewache-Client
echo - Alle JavaFX-Abhängigkeiten
echo - Eingebettete Java-Runtime
echo - Professionellen Installer mit GUI
echo.
echo Benutzer muessen nur diese .exe-Datei herunterladen und ausfuehren.
echo Keine weiteren Installationen oder Downloads erforderlich!
echo.

echo Installer testen? (J/N)
set /p test="Eingabe: "
if /i "%test%"=="J" (
    echo Starte Installer...
    start "" "build\installer\MinewacheClient-Installer-1.0.0.exe"
)

echo.
echo Build-Anleitung: BUILD_GUIDE.md
echo Installer-Anleitung: INSTALLER_GUIDE.md
pause
