# Installer Configuration

This directory contains the configuration files for creating a professional Windows installer using Inno Setup.

## Files

### Core Configuration
- **`minewache-installer.iss`** - Main Inno Setup script
- **`install-info.txt`** - Information shown before installation
- **`install-complete.txt`** - Information shown after installation

### Optional Customization
- **`wizard-image.bmp`** - Large installer image (164x314 pixels)
- **`wizard-small.bmp`** - Small installer image (55x58 pixels)

## Usage

### Quick Build
```cmd
build-installer-complete.bat
```

### Manual Build
```cmd
gradlew clean shadowJar jpackage createInstaller
```

## Requirements

- **Inno Setup 5 or 6** (https://jrsoftware.org/isinfo.php)
- **Icon file** at `src/main/resources/icons/logo.ico`
- **License file** at `LICENSE.txt`

## Output

The installer will be created at:
```
build/installer/MinewacheClient-Installer-1.0.0.exe
```

## Customization

### Changing App Information
Edit `minewache-installer.iss`:
```pascal
#define MyAppName "Your App Name"
#define MyAppVersion "1.0.0"
#define MyAppPublisher "Your Company"
```

### Adding Custom Images
1. Create `wizard-image.bmp` (164x314 pixels)
2. Create `wizard-small.bmp` (55x58 pixels)
3. Uncomment image lines in the script

### Modifying Installation Text
- Edit `install-info.txt` for pre-installation information
- Edit `install-complete.txt` for post-installation information

## Features

The installer provides:
- ✅ GUI installation with path selection
- ✅ Desktop shortcut option
- ✅ Start menu entry option
- ✅ Launch after installation option
- ✅ Progress indication
- ✅ Automatic upgrade detection
- ✅ Professional uninstaller
- ✅ Registry integration
- ✅ No admin rights required

## Distribution

Users only need to download and run:
```
MinewacheClient-Installer-1.0.0.exe
```

No additional downloads or installations required!
