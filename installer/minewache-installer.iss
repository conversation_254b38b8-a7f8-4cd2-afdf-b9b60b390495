; Minewache-Client Installer Script für Inno Setup
; Erstellt einen professionellen Windows-Installer für die Minewache-Client Anwendung

#define MyAppName "Minewache Launcher"
#define MyAppVersion "1.0.0"
#define MyAppPublisher "Die Minewache"
#define MyAppURL "https://dieminewache.de"
#define MyAppExeName "MinewacheClient.exe"
#define MyAppDescription "Minecraft Launcher für Die Minewache Server"

[Setup]
; Grundlegende Installer-Informationen
AppId={{B8E8F8A0-1234-5678-9ABC-DEF012345678}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppVerName={#MyAppName} {#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
AppCopyright=© 2025 {#MyAppPublisher}

; Standard-Installationspfad
DefaultDirName={autopf}\{#MyAppName}
DefaultGroupName={#MyAppName}
AllowNoIcons=yes

; Ausgabe-Konfiguration
OutputDir=..\build\installer
OutputBaseFilename=MinewacheClient-Installer-{#MyAppVersion}
SetupIconFile=..\src\main\resources\icons\logo.ico
UninstallDisplayIcon={app}\{#MyAppExeName}

; Komprimierung und Verschlüsselung
Compression=lzma2/ultra64
SolidCompression=yes
WizardStyle=modern

; Berechtigungen und Kompatibilität
PrivilegesRequired=lowest
PrivilegesRequiredOverridesAllowed=dialog
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

; Lizenz und Info-Dateien
LicenseFile=..\LICENSE.txt
InfoBeforeFile=..\installer\install-info.txt
InfoAfterFile=..\installer\install-complete.txt

; Wizard-Konfiguration
WizardImageFile=..\installer\wizard-image.bmp
WizardSmallImageFile=..\installer\wizard-small.bmp
DisableWelcomePage=no
DisableDirPage=no
DisableProgramGroupPage=no
DisableReadyPage=no
DisableFinishedPage=no

[Languages]
Name: "german"; MessagesFile: "compiler:Languages\German.isl"
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1; Check: not IsAdminInstallMode
Name: "startmenuicon"; Description: "Startmenü-Eintrag erstellen"; GroupDescription: "{cm:AdditionalIcons}"; Flags: checkedonce
Name: "launchafter"; Description: "Anwendung nach Installation starten"; GroupDescription: "Nach der Installation:"; Flags: checkedonce

[Files]
; Hauptanwendung und alle Abhängigkeiten
Source: "..\build\jpackage\MinewacheClient\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs
; Zusätzliche Konfigurationsdateien
Source: "..\README.md"; DestDir: "{app}"; Flags: ignoreversion
Source: "..\LICENSE.txt"; DestDir: "{app}"; Flags: ignoreversion; DestName: "LICENSE.txt"

[Icons]
; Startmenü-Verknüpfung
Name: "{group}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; WorkingDir: "{app}"; IconFilename: "{app}\{#MyAppExeName}"; Tasks: startmenuicon
Name: "{group}\{cm:UninstallProgram,{#MyAppName}}"; Filename: "{uninstallexe}"; Tasks: startmenuicon

; Desktop-Verknüpfung
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; WorkingDir: "{app}"; IconFilename: "{app}\{#MyAppExeName}"; Tasks: desktopicon

; Schnellstart-Verknüpfung (nur für ältere Windows-Versionen)
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; WorkingDir: "{app}"; Tasks: quicklaunchicon

[Registry]
; Anwendungsregistrierung für "Programme hinzufügen/entfernen"
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\{#MyAppName}"; ValueType: string; ValueName: "DisplayName"; ValueData: "{#MyAppName}"; Flags: uninsdeletekey
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\{#MyAppName}"; ValueType: string; ValueName: "DisplayVersion"; ValueData: "{#MyAppVersion}"; Flags: uninsdeletekey
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\{#MyAppName}"; ValueType: string; ValueName: "Publisher"; ValueData: "{#MyAppPublisher}"; Flags: uninsdeletekey
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\{#MyAppName}"; ValueType: string; ValueName: "URLInfoAbout"; ValueData: "{#MyAppURL}"; Flags: uninsdeletekey
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\{#MyAppName}"; ValueType: string; ValueName: "DisplayIcon"; ValueData: "{app}\{#MyAppExeName}"; Flags: uninsdeletekey

; Dateierweiterungen (falls benötigt)
Root: HKCR; Subkey: ".minewache"; ValueType: string; ValueName: ""; ValueData: "MinewacheFile"; Flags: uninsdeletevalue
Root: HKCR; Subkey: "MinewacheFile"; ValueType: string; ValueName: ""; ValueData: "Minewache Configuration File"; Flags: uninsdeletekey
Root: HKCR; Subkey: "MinewacheFile\DefaultIcon"; ValueType: string; ValueName: ""; ValueData: "{app}\{#MyAppExeName},0"
Root: HKCR; Subkey: "MinewacheFile\shell\open\command"; ValueType: string; ValueName: ""; ValueData: """{app}\{#MyAppExeName}"" ""%1"""

[Run]
; Nach der Installation ausführen (optional)
Filename: "{app}\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#StringChange(MyAppName, '&', '&&')}}"; Flags: nowait postinstall skipifsilent; Tasks: launchafter

[UninstallDelete]
; Zusätzliche Dateien beim Deinstallieren löschen
Type: filesandordirs; Name: "{app}\logs"
Type: filesandordirs; Name: "{app}\cache"
Type: filesandordirs; Name: "{app}\temp"

[Code]
// Benutzerdefinierte Funktionen für erweiterte Installer-Logik

function GetUninstallString(): String;
var
  sUnInstPath: String;
  sUnInstallString: String;
begin
  sUnInstPath := ExpandConstant('Software\Microsoft\Windows\CurrentVersion\Uninstall\{#emit SetupSetting("AppId")}');
  sUnInstallString := '';
  if not RegQueryStringValue(HKLM, sUnInstPath, 'UninstallString', sUnInstallString) then
    RegQueryStringValue(HKCU, sUnInstPath, 'UninstallString', sUnInstallString);
  Result := sUnInstallString;
end;

function IsUpgrade(): Boolean;
begin
  Result := (GetUninstallString() <> '');
end;

function UnInstallOldVersion(): Integer;
var
  sUnInstallString: String;
  iResultCode: Integer;
begin
  Result := 0;
  sUnInstallString := GetUninstallString();
  if sUnInstallString <> '' then begin
    sUnInstallString := RemoveQuotes(sUnInstallString);
    if Exec(sUnInstallString, '/SILENT /NORESTART /SUPPRESSMSGBOXES','', SW_HIDE, ewWaitUntilTerminated, iResultCode) then
      Result := 3
    else
      Result := 2;
  end else
    Result := 1;
end;

procedure CurStepChanged(CurStep: TSetupStep);
begin
  if (CurStep=ssInstall) then
  begin
    if (IsUpgrade()) then
    begin
      UnInstallOldVersion();
    end;
  end;
end;

function InitializeSetup(): Boolean;
var
  V: Integer;
  iResultCode: Integer;
  sUnInstallString: String;
begin
  Result := True;
  
  // Prüfe auf vorhandene Installation
  if RegValueExists(HKEY_LOCAL_MACHINE,'Software\Microsoft\Windows\CurrentVersion\Uninstall\{#MyAppName}', 'UninstallString') then
  begin
    V := MsgBox(ExpandConstant('Eine ältere Version von {#MyAppName} ist bereits installiert. Möchten Sie diese vor der Installation der neuen Version deinstallieren?'), mbInformation, MB_YESNO);
    if V = IDYES then
    begin
      RegQueryStringValue(HKEY_LOCAL_MACHINE,'Software\Microsoft\Windows\CurrentVersion\Uninstall\{#MyAppName}', 'UninstallString', sUnInstallString);
      sUnInstallString := RemoveQuotes(sUnInstallString);
      Exec(sUnInstallString, '/SILENT', '', SW_HIDE, ewWaitUntilTerminated, iResultCode);
      if iResultCode <> 0 then
      begin
        MsgBox('Fehler beim Deinstallieren der alten Version. Installation wird abgebrochen.', mbError, MB_OK);
        Result := False;
      end;
    end else begin
      MsgBox('Installation wird abgebrochen.', mbInformation, MB_OK);
      Result := False;
    end;
  end;
end;

// Fortschrittsanzeige während der Installation
procedure CurInstallProgressChanged(CurProgress, MaxProgress: Integer);
var
  ProgressPercent: Integer;
begin
  if MaxProgress > 0 then
  begin
    ProgressPercent := (CurProgress * 100) div MaxProgress;
    WizardForm.StatusLabel.Caption := Format('Installation läuft... %d%%', [ProgressPercent]);
  end;
end;
