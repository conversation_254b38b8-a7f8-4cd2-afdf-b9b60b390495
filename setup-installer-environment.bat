@echo off
echo ========================================
echo Minewache-Client Installer Environment Setup
echo ========================================

echo.
echo Dieses Script hilft beim Einrichten der Umgebung
echo fuer die Installer-Erstellung.
echo.

:: Prüfe Java
echo Schritt 1: Java-Version pruefen...
java -version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [FEHLER] Java nicht gefunden!
    echo Bitte installieren Sie Java 21 oder hoeher.
    echo Download: https://adoptium.net/
    echo.
    pause
    exit /b 1
) else (
    echo [OK] Java gefunden
    java -version
)

echo.

:: Prüfe Inno Setup
echo Schritt 2: Inno Setup pruefen...
set "INNO_SETUP_FOUND=0"

if exist "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" (
    echo [OK] Inno Setup 6 gefunden: C:\Program Files (x86)\Inno Setup 6\
    set "INNO_SETUP_FOUND=1"
) else if exist "C:\Program Files\Inno Setup 6\ISCC.exe" (
    echo [OK] Inno Setup 6 gefunden: C:\Program Files\Inno Setup 6\
    set "INNO_SETUP_FOUND=1"
) else if exist "C:\Program Files (x86)\Inno Setup 5\ISCC.exe" (
    echo [OK] Inno Setup 5 gefunden: C:\Program Files (x86)\Inno Setup 5\
    set "INNO_SETUP_FOUND=1"
) else if exist "C:\Program Files\Inno Setup 5\ISCC.exe" (
    echo [OK] Inno Setup 5 gefunden: C:\Program Files\Inno Setup 5\
    set "INNO_SETUP_FOUND=1"
)

if "%INNO_SETUP_FOUND%"=="0" (
    echo [WARNUNG] Inno Setup nicht gefunden!
    echo.
    echo Inno Setup wird fuer die Installer-Erstellung benoetigt.
    echo.
    echo Moechten Sie Inno Setup jetzt herunterladen? (J/N)
    set /p download="Eingabe: "
    if /i "%download%"=="J" (
        echo Oeffne Inno Setup Download-Seite...
        start https://jrsoftware.org/isinfo.php
        echo.
        echo Bitte installieren Sie Inno Setup und fuehren Sie dieses Script erneut aus.
        pause
        exit /b 1
    )
)

echo.

:: Prüfe Icon-Datei
echo Schritt 3: Icon-Datei pruefen...
if exist "src\main\resources\icons\logo.ico" (
    echo [OK] Icon-Datei gefunden: src\main\resources\icons\logo.ico
) else (
    echo [WARNUNG] Icon-Datei nicht gefunden!
    echo.
    echo Erstellen Sie die Datei: src\main\resources\icons\logo.ico
    echo.
    echo Icon-Anforderungen:
    echo - Format: .ico (Windows Icon)
    echo - Groessen: 16x16, 32x32, 48x48, 256x256 Pixel
    echo - Farbtiefe: 32-bit mit Alpha-Kanal
    echo.
    echo Sie koennen Online-Konverter verwenden:
    echo - https://convertio.co/png-ico/
    echo - https://www.icoconverter.com/
    echo.
)

echo.

:: Prüfe Installer-Dateien
echo Schritt 4: Installer-Konfiguration pruefen...
if exist "installer\minewache-installer.iss" (
    echo [OK] Installer-Script gefunden: installer\minewache-installer.iss
) else (
    echo [FEHLER] Installer-Script nicht gefunden!
    echo Datei fehlt: installer\minewache-installer.iss
)

if exist "installer\install-info.txt" (
    echo [OK] Installations-Info gefunden: installer\install-info.txt
) else (
    echo [WARNUNG] Installations-Info nicht gefunden: installer\install-info.txt
)

if exist "installer\install-complete.txt" (
    echo [OK] Abschluss-Info gefunden: installer\install-complete.txt
) else (
    echo [WARNUNG] Abschluss-Info nicht gefunden: installer\install-complete.txt
)

if exist "LICENSE.txt" (
    echo [OK] Lizenz-Datei gefunden: LICENSE.txt
) else (
    echo [WARNUNG] Lizenz-Datei nicht gefunden: LICENSE.txt
)

echo.

:: Zusammenfassung
echo ========================================
echo UMGEBUNGS-STATUS
echo ========================================

if "%INNO_SETUP_FOUND%"=="1" (
    echo [OK] Alle erforderlichen Tools sind installiert
    echo.
    echo Sie koennen jetzt den Installer erstellen:
    echo.
    echo   build-installer-complete.bat
    echo.
    echo Oder manuell:
    echo.
    echo   gradlew clean shadowJar jpackage createInstaller
    echo.
) else (
    echo [FEHLER] Inno Setup fehlt - Installer-Erstellung nicht moeglich
    echo.
    echo Bitte installieren Sie Inno Setup von:
    echo https://jrsoftware.org/isinfo.php
    echo.
)

echo Weitere Informationen:
echo - BUILD_GUIDE.md (App-Image Erstellung)
echo - INSTALLER_GUIDE.md (Installer-Erstellung)
echo.

pause
