plugins {
    id("java")
    id("application")
    id("org.openjfx.javafxplugin") version "0.0.13"
    id("org.graalvm.buildtools.native") version "0.10.1" // Keep for optional native image builds
    id("com.github.johnrengelman.shadow") version "8.1.1" // For creating fat JARs
}

group = "de.sarocesch"
version = "1.0-SNAPSHOT"

repositories {
    mavenCentral()
}

tasks.withType<JavaCompile> {
    options.encoding = "UTF-8"
}


dependencies {
    implementation("org.openjfx:javafx-controls:21.0.1")
    implementation("org.openjfx:javafx-fxml:21.0.1")
    implementation("org.openjfx:javafx-graphics:21.0.1")
    implementation("org.openjfx:javafx-base:21.0.1")
    implementation("org.openjfx:javafx-web:21.0.1")
    implementation("org.controlsfx:controlsfx:11.1.2")
    implementation("com.google.code.gson:gson:2.8.9")
    implementation("org.commonmark:commonmark:0.17.0") // Verify if this or atlassian.commonmark is preferred/needed. Remove duplicate.
    implementation("com.hierynomus:sshj:0.35.0")
    testImplementation(platform("org.junit:junit-bom:5.10.0"))
    testImplementation("org.junit.jupiter:junit-jupiter")
    implementation("com.jcraft:jsch:0.1.55")
    // implementation("com.atlassian.commonmark:commonmark:0.17.0") // Commented out, assuming org.commonmark is the primary one. Please verify.
}

application {
    mainClass.set("de.sarocesch.Main")
}

javafx {
    version = "21.0.1"
    modules = listOf("javafx.controls", "javafx.fxml", "javafx.web", "javafx.graphics")
}

// Shadow JAR Konfiguration für Fat JAR
tasks.shadowJar {
    archiveClassifier.set("") // Keine Classifier, damit es die Standard-JAR ersetzt
    mergeServiceFiles() // Merge service files

    // Exclude JavaFX modules from fat JAR (they will be provided by module-path)
    exclude("module-info.class")

    // Manifest für JavaFX
    manifest {
        attributes(
            "Main-Class" to "de.sarocesch.Main"
        )
    }
}

// Custom jpackage task for creating Windows installer
tasks.register("jpackage") {
    dependsOn("shadowJar")
    group = "distribution"
    description = "Create a native installer using jpackage"

    doLast {
        val javaHome = System.getProperty("java.home")
        val jpackageCmd = "$javaHome/bin/jpackage"

        val buildDir = layout.buildDirectory.get().asFile
        val libsDir = File(buildDir, "libs")
        val jarFile = File(libsDir, "${project.name}-${project.version}.jar") // Shadow JAR ohne Classifier

        if (!jarFile.exists()) {
            throw GradleException("JAR file not found: ${jarFile.absolutePath}")
        }

        // Get JavaFX runtime classpath (nur für JavaFX Module)
        val javafxClasspath = configurations.getByName("runtimeClasspath").files
            .filter { it.name.startsWith("javafx-") }
            .joinToString(File.pathSeparator) { it.absolutePath }

        val outputDir = File(buildDir, "jpackage")
        // Lösche das alte Verzeichnis falls es existiert
        if (outputDir.exists()) {
            outputDir.deleteRecursively()
        }
        outputDir.mkdirs()

        val command = mutableListOf(
            jpackageCmd,
            "--type", "app-image", // Create app-image first (doesn't require WiX)
            "--input", libsDir.absolutePath,
            "--dest", outputDir.absolutePath,
            "--name", "MinewacheClient",
            "--main-class", "de.sarocesch.Main",
            "--main-jar", jarFile.name,
            "--module-path", javafxClasspath, // Nur JavaFX Module
            "--add-modules", "javafx.controls,javafx.fxml,javafx.web,javafx.graphics,java.base,java.desktop",
            "--app-version", "1.0.0",
            "--vendor", "Die Minewache",
            "--description", "Minewache Minecraft Launcher",
            "--java-options", "-Xmx2G",
            "--java-options", "-Dprism.order=d3d,sw",
            "--java-options", "-Dprism.verbose=false",
            "--java-options", "-Djavafx.verbose=false",
            "--java-options", "-Djava.awt.headless=false"
            // Keine --win-console Option = keine CMD-Fenster
        )

        // Add application icon
        val iconFile = file("src/main/resources/icons/logo.ico")
        if (iconFile.exists()) {
            command.addAll(listOf("--icon", iconFile.absolutePath))
            println("Using application icon: ${iconFile.absolutePath}")
        } else {
            println("Warning: Icon file not found at ${iconFile.absolutePath}")
        }

        println("Executing jpackage command:")
        println(command.joinToString(" "))

        val processBuilder = ProcessBuilder(command)
            .directory(projectDir)

        // Capture output for better error reporting
        val process = processBuilder.start()

        // Read output streams
        val outputReader = process.inputStream.bufferedReader()
        val errorReader = process.errorStream.bufferedReader()

        val outputThread = Thread {
            outputReader.useLines { lines ->
                lines.forEach { println("OUT: $it") }
            }
        }

        val errorThread = Thread {
            errorReader.useLines { lines ->
                lines.forEach { println("ERR: $it") }
            }
        }

        outputThread.start()
        errorThread.start()

        val exitCode = process.waitFor()
        outputThread.join()
        errorThread.join()

        if (exitCode != 0) {
            throw GradleException("jpackage failed with exit code: $exitCode")
        }

        println("jpackage completed successfully!")
        println("App-image created in: ${outputDir.absolutePath}")
    }
}

// Task to create Inno Setup installer
tasks.register("createInstaller") {
    dependsOn("jpackage")
    group = "distribution"
    description = "Create Windows installer using Inno Setup"

    doLast {
        val buildDir = layout.buildDirectory.get().asFile
        val installerDir = File(buildDir, "installer")

        // Erstelle Installer-Verzeichnis
        if (!installerDir.exists()) {
            installerDir.mkdirs()
        }

        // Prüfe ob Inno Setup installiert ist
        val innoSetupPaths = listOf(
            "C:\\Program Files (x86)\\Inno Setup 6\\ISCC.exe",
            "C:\\Program Files\\Inno Setup 6\\ISCC.exe",
            "C:\\Program Files (x86)\\Inno Setup 5\\ISCC.exe",
            "C:\\Program Files\\Inno Setup 5\\ISCC.exe"
        )

        val isccPath = innoSetupPaths.find { File(it).exists() }

        if (isccPath == null) {
            throw GradleException("""
                Inno Setup not found! Please install Inno Setup from:
                https://jrsoftware.org/isinfo.php

                Searched in:
                ${innoSetupPaths.joinToString("\n")}
            """.trimIndent())
        }

        val scriptFile = file("installer/minewache-installer.iss")
        if (!scriptFile.exists()) {
            throw GradleException("Installer script not found: ${scriptFile.absolutePath}")
        }

        println("Using Inno Setup: $isccPath")
        println("Compiling installer script: ${scriptFile.absolutePath}")

        val command = listOf(
            isccPath,
            "/Q", // Quiet mode
            scriptFile.absolutePath
        )

        val processBuilder = ProcessBuilder(command)
            .directory(projectDir)

        val process = processBuilder.start()

        // Read output streams
        val outputReader = process.inputStream.bufferedReader()
        val errorReader = process.errorStream.bufferedReader()

        val outputThread = Thread {
            outputReader.useLines { lines ->
                lines.forEach { println("INNO OUT: $it") }
            }
        }

        val errorThread = Thread {
            errorReader.useLines { lines ->
                lines.forEach { println("INNO ERR: $it") }
            }
        }

        outputThread.start()
        errorThread.start()

        val exitCode = process.waitFor()
        outputThread.join()
        errorThread.join()

        if (exitCode != 0) {
            throw GradleException("Inno Setup compilation failed with exit code: $exitCode")
        }

        val installerFile = File(buildDir, "installer/MinewacheClient-Installer-1.0.0.exe")
        if (installerFile.exists()) {
            println("Installer created successfully!")
            println("Installer location: ${installerFile.absolutePath}")
            println("File size: ${installerFile.length() / 1024 / 1024} MB")
        } else {
            println("Warning: Installer file not found at expected location")
        }
    }
}

graalvmNative {
    binaries {
        named("main") {
            imageName.set("MinewacheClient")
            mainClass.set("de.sarocesch.Main")

            // Umfassende Build-Argumente für JavaFX
            buildArgs.addAll(
                "--no-fallback",
                "--verbose",

                // Grundlegende Konfiguration
                "--enable-http",
                "--enable-https",
                "--enable-url-protocols=http,https,file",
                "--report-unsupported-elements-at-runtime",
                "-H:+ReportExceptionStackTraces",
                "--allow-incomplete-classpath",
                "-H:+UnlockExperimentalVMOptions",

                // JavaFX-Module explizit hinzufügen
                "--add-modules=javafx.controls,javafx.fxml,javafx.web,javafx.graphics,javafx.base",

                // Konfigurationsdateien
                "-H:ConfigurationFileDirectories=${project.projectDir}/src/main/resources/META-INF/native-image",

                // JavaFX Initialisierung - kritisch für native images
                "--initialize-at-run-time=javafx.scene.image.Image",
                "--initialize-at-run-time=javafx.scene.media",
                "--initialize-at-run-time=javafx.scene.web",
                "--initialize-at-run-time=javafx.application.Platform",
                "--initialize-at-run-time=com.sun.javafx.tk.Toolkit",
                "--initialize-at-run-time=com.sun.javafx",
                "--initialize-at-run-time=com.sun.glass",
                "--initialize-at-run-time=com.sun.prism",
                "--initialize-at-run-time=com.sun.scenario",
                "--initialize-at-run-time=com.sun.webkit",
                "--initialize-at-run-time=com.sun.prism.impl.PrismSettings",
                "--initialize-at-run-time=com.sun.prism.GraphicsPipeline",

                // Debugging für Initialisierungsprobleme (kann nach erfolgreichem Build entfernt werden)
                "--trace-class-initialization=javafx.scene.image.Image,com.sun.javafx.tk.Toolkit,javafx.application.Platform,com.sun.prism.impl.PrismSettings",

                // Reflection für JavaFX
                "-H:+AddAllCharsets",
                "-H:IncludeResourceBundles=com.sun.javafx.scene.control.skin.resources.controls",

                // Native Bibliotheken für Windows
                "-H:+AllowIncompleteClasspath",
                "-H:+ReportUnsupportedElementsAtRuntime",

                // Alle JavaFX nativen Bibliotheken einbetten
                "-H:IncludeResources=.*\\.dll$",
                "-H:IncludeResources=.*prism_.*",
                "-H:IncludeResources=.*glass.*",
                "-H:IncludeResources=.*javafx_.*",
                "-H:IncludeResources=.*jfxwebkit.*",
                "-H:IncludeResources=.*gstreamer.*",
                "-H:IncludeResources=.*fxplugins.*",

                // Windows-spezifische DLLs
                "-H:IncludeResources=.*api-ms-win-.*\\.dll$",
                "-H:IncludeResources=.*vcruntime.*\\.dll$",
                "-H:IncludeResources=.*msvcp.*\\.dll$",
                "-H:IncludeResources=.*ucrtbase.*\\.dll$",

                // JNI Konfiguration
                "-H:+JNI",
                "-H:+AllowVMInspection",

                // Notwendige Öffnungen für JavaFX
                "--add-opens=javafx.graphics/com.sun.javafx.tk=ALL-UNNAMED",
                "--add-opens=javafx.graphics/com.sun.javafx.tk.quantum=ALL-UNNAMED",
                "--add-opens=javafx.graphics/com.sun.glass.ui=ALL-UNNAMED",
                "--add-opens=javafx.graphics/com.sun.glass.ui.win=ALL-UNNAMED",
                "--add-opens=javafx.graphics/com.sun.javafx.application=ALL-UNNAMED",
                "--add-opens=javafx.graphics/com.sun.prism=ALL-UNNAMED",
                "--add-opens=javafx.graphics/com.sun.prism.d3d=ALL-UNNAMED",
                "--add-opens=javafx.graphics/com.sun.prism.sw=ALL-UNNAMED",
                "--add-opens=javafx.graphics/com.sun.javafx.scene=ALL-UNNAMED",
                "--add-opens=javafx.graphics/com.sun.javafx.sg.prism=ALL-UNNAMED",
                "--add-opens=javafx.graphics/com.sun.scenario.effect=ALL-UNNAMED",
                "--add-opens=javafx.graphics/com.sun.scenario.effect.impl.prism=ALL-UNNAMED",
                "--add-opens=javafx.graphics/com.sun.scenario.effect.impl.prism.ps=ALL-UNNAMED",
                "--add-opens=javafx.graphics/com.sun.scenario.effect.impl.prism.sw=ALL-UNNAMED",
                "--add-opens=javafx.controls/com.sun.javafx.scene.control=ALL-UNNAMED",
                "--add-opens=javafx.controls/com.sun.javafx.scene.control.behavior=ALL-UNNAMED",
                "--add-opens=javafx.controls/com.sun.javafx.scene.control.skin=ALL-UNNAMED",
                "--add-opens=javafx.base/com.sun.javafx.runtime=ALL-UNNAMED",
                "--add-opens=javafx.base/com.sun.javafx.collections=ALL-UNNAMED",
                "--add-opens=java.base/java.lang.reflect=ALL-UNNAMED",
                "--add-opens=java.base/java.lang=ALL-UNNAMED"
            )

            // Dynamically construct and add the JavaFX module path
            val javafxModulePathEntries = project.configurations.getByName("runtimeClasspath").files
                .filter { it.name.startsWith("javafx-") && it.extension == "jar" }
                .map { it.absolutePath }

            if (javafxModulePathEntries.isNotEmpty()) {
                val javafxModulePath = javafxModulePathEntries.joinToString(System.getProperty("path.separator"))
                buildArgs.add("--module-path")
                buildArgs.add(javafxModulePath)
            }

            // Füge die nativen Bibliotheken hinzu
            val nativesPath = findJavaFXNativesPath()
            buildArgs.add("-H:CLibraryPath=${nativesPath}")
            buildArgs.add("-H:Path=${nativesPath}")
        }
    }
}

// Funktion zum Finden des Pfads zu den JavaFX-nativen Bibliotheken
fun findJavaFXNativesPath(): String {
    val nativesDir = File(project.layout.buildDirectory.get().asFile, "javafx-natives")
    if (!nativesDir.exists()) {
        nativesDir.mkdirs()
    }
    return nativesDir.absolutePath
}

// Erweiterte Task zum Extrahieren aller benötigten nativen Bibliotheken
tasks.register("extractJavaFXNatives") {
    doLast {
        val osName = System.getProperty("os.name").lowercase()
        val osDir = when {
            osName.contains("win") -> "win"
            osName.contains("mac") || osName.contains("darwin") -> "mac"
            osName.contains("linux") || osName.contains("unix") -> "linux"
            else -> throw GradleException("Nicht unterstütztes Betriebssystem: $osName")
        }

        val nativesDir = File(project.layout.buildDirectory.get().asFile, "javafx-natives")
        if (!nativesDir.exists()) {
            nativesDir.mkdirs()
        }

        println("Extrahiere JavaFX native Bibliotheken für $osDir nach $nativesDir")

        // Alle JavaFX Module mit nativen Bibliotheken
        val javafxModules = listOf("graphics", "controls", "fxml", "web", "media")

        javafxModules.forEach { module ->
            try {
                val javafxNatives = configurations.detachedConfiguration(
                    dependencies.create("org.openjfx:javafx-$module:${javafx.version}:$osDir")
                )

                copy {
                    from(javafxNatives.map { zipTree(it) })
                    into(nativesDir)
                    include("**/*.dll", "**/*.so", "**/*.dylib")
                    duplicatesStrategy = DuplicatesStrategy.INCLUDE
                }
                println("Extrahiert: javafx-$module")
            } catch (e: Exception) {
                println("Warnung: Konnte javafx-$module nicht extrahieren: ${e.message}")
            }
        }

        // Zusätzlich: Extrahiere alle nativen Bibliotheken aus den Haupt-JAR-Dateien
        project.configurations.getByName("runtimeClasspath").files
            .filter { it.name.startsWith("javafx-") && it.extension == "jar" }
            .forEach { jarFile ->
                try {
                    copy {
                        from(zipTree(jarFile))
                        into(nativesDir)
                        include("**/*.dll", "**/*.so", "**/*.dylib")
                        include("**/prism_*")
                        include("**/glass*")
                        include("**/javafx_*")
                        include("**/jfxwebkit*")
                        include("**/gstreamer*")
                        include("**/fxplugins*")
                        duplicatesStrategy = DuplicatesStrategy.INCLUDE
                    }
                    println("Extrahiert native Bibliotheken aus: ${jarFile.name}")
                } catch (e: Exception) {
                    println("Warnung: Konnte ${jarFile.name} nicht extrahieren: ${e.message}")
                }
            }

        // Liste alle extrahierten Dateien auf
        val extractedFiles = nativesDir.walkTopDown().filter { it.isFile }.toList()
        println("Extrahierte ${extractedFiles.size} native Bibliotheken:")
        extractedFiles.forEach { println("  - ${it.name}") }
    }
}

// Task zum Kopieren der nativen Bibliotheken in das Build-Verzeichnis
tasks.register("copyNativesToBuild") {
    dependsOn("extractJavaFXNatives")
    doLast {
        val nativesDir = File(project.layout.buildDirectory.get().asFile, "javafx-natives")
        val buildDir = File(project.layout.buildDirectory.get().asFile, "native/nativeCompile")

        if (nativesDir.exists() && buildDir.exists()) {
            copy {
                from(nativesDir)
                into(buildDir)
                include("**/*.dll", "**/*.so", "**/*.dylib")
            }
            println("Native Bibliotheken in Build-Verzeichnis kopiert")
        }
    }
}

// Füge eine Abhängigkeit hinzu, damit die Tasks vor dem nativen Build ausgeführt werden
tasks.named("nativeCompile") {
    dependsOn("extractJavaFXNatives", "copyNativesToBuild")
}


