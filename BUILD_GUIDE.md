# Minewache-Client Build-Anleitung

## Vollständige Anleitung zum Erstellen der Windows-Anwendung mit benutzerdefiniertem Icon

---

## 📋 Voraussetzungen

### Erforderliche Software:
- **Java Development Kit (JDK) 21** oder höher
- **Git** (für Versionskontrolle)
- **Windows 10/11** (für Windows-spezifische Builds)
- **Visual Studio Build Tools** (für native Kompilierung, falls benötigt)

### Umgebung prüfen:
```cmd
java -version
javac -version
```
**Erwartete Ausgabe:** Java 21.x.x oder höher

---

## 🏗️ Build-Prozess Schritt für Schritt

### 1. Repository klonen und Verzeichnis wechseln
```cmd
git clone <repository-url>
cd Minewache-Client
```

### 2. Projekt bereinigen (optional, aber empfohlen)
```cmd
gradlew clean
```

### 3. Shadow JAR erstellen (Fat JAR mit allen Abhängigkeiten)
```cmd
gradlew shadowJar
```
**Was passiert:** Erstellt eine JAR-Datei mit allen Abhängigkeiten (SSHJ, JavaFX, etc.)

### 4. jpackage-Anwendung erstellen
```cmd
gradlew jpackage
```
**Was passiert:** 
- Verwendet die Shadow JAR
- Erstellt eine eigenständige Windows-Anwendung
- Bindet das benutzerdefinierte Icon ein
- Erstellt eine portable Anwendung mit eingebetteter Java-Runtime

### 5. Vollständiger Build-Befehl (empfohlen)
```cmd
gradlew clean shadowJar jpackage
```

---

## 📁 Dateispeicherorte

### Generierte Dateien:
```
build/
├── libs/
│   └── Minewache-Client-1.0-SNAPSHOT.jar    # Shadow JAR (Fat JAR)
└── jpackage/
    └── MinewacheClient/
        ├── MinewacheClient.exe               # Hauptanwendung
        ├── app/                              # Anwendungsdateien
        └── runtime/                          # Java Runtime
```

### Icon-Datei:
```
src/main/resources/icons/logo.ico             # Benutzerdefiniertes Icon
```

---

## 🎨 Icon-Konfiguration

### Icon-Anforderungen:
- **Format:** .ico (Windows Icon)
- **Empfohlene Größen:** 16x16, 32x32, 48x48, 256x256 Pixel
- **Farbtiefe:** 32-bit mit Alpha-Kanal
- **Speicherort:** `src/main/resources/icons/logo.ico`

### Icon-Verifikation:
1. **Build-Log prüfen:**
   ```
   Using application icon: T:\...\src\main\resources\icons\logo.ico
   ```

2. **Datei-Eigenschaften prüfen:**
   - Rechtsklick auf `MinewacheClient.exe`
   - "Eigenschaften" auswählen
   - Icon sollte in der Registerkarte "Allgemein" sichtbar sein

3. **Windows Explorer:**
   - Icon sollte neben der .exe-Datei angezeigt werden

---

## 🔧 Fehlerbehebung

### Problem: Icon wird nicht angezeigt
**Lösung 1:** Icon-Cache leeren
```cmd
ie4uinit.exe -show
```

**Lösung 2:** Neustart des Windows Explorers
```cmd
taskkill /f /im explorer.exe
start explorer.exe
```

**Lösung 3:** Icon-Datei prüfen
```cmd
dir src\main\resources\icons\logo.ico
```

### Problem: Build schlägt fehl
**Lösung 1:** Java-Version prüfen
```cmd
java -version
```
**Erforderlich:** Java 21 oder höher

**Lösung 2:** Gradle Daemon neu starten
```cmd
gradlew --stop
gradlew clean shadowJar jpackage
```

### Problem: Anwendung startet nicht
**Lösung 1:** Mit Konsole testen
```cmd
cd build\jpackage\MinewacheClient
MinewacheClient.exe
```

**Lösung 2:** Java-Optionen prüfen (in build.gradle.kts)
```kotlin
"--java-options", "-Xmx2G",
"--java-options", "-Dprism.order=d3d,sw"
```

---

## 📦 Verteilung

### Für Endbenutzer:
1. **Gesamtes Verzeichnis komprimieren:**
   ```cmd
   cd build\jpackage
   tar -czf MinewacheClient-Windows.zip MinewacheClient/
   ```

2. **Verteilungsinhalt:**
   - `MinewacheClient.exe` (Hauptanwendung)
   - `app/` Verzeichnis (erforderlich)
   - `runtime/` Verzeichnis (erforderlich)

### Benutzeranweisungen:
1. ZIP-Datei entpacken
2. `MinewacheClient.exe` ausführen
3. **Keine Java-Installation erforderlich** (Runtime ist eingebettet)

---

## ⚙️ Build-Konfiguration (Erweitert)

### Wichtige Gradle-Tasks:
- `clean` - Bereinigt vorherige Builds
- `shadowJar` - Erstellt Fat JAR mit allen Abhängigkeiten
- `jpackage` - Erstellt native Windows-Anwendung

### jpackage-Parameter (in build.gradle.kts):
```kotlin
"--type", "app-image"                    // Anwendungstyp
"--name", "MinewacheClient"              // Anwendungsname
"--main-class", "de.sarocesch.Main"      // Hauptklasse
"--icon", iconFile.absolutePath          // Benutzerdefiniertes Icon
"--java-options", "-Xmx2G"               // JVM-Optionen
```

---

## ✅ Erfolgreiche Build-Verifikation

### Checkliste:
- [ ] Build ohne Fehler abgeschlossen
- [ ] `MinewacheClient.exe` existiert
- [ ] Icon wird in Windows Explorer angezeigt
- [ ] Anwendung startet ohne CMD-Fenster
- [ ] JavaFX-Fenster öffnet sich korrekt

### Finale Ausgabe:
```
BUILD SUCCESSFUL in Xs
jpackage completed successfully!
Installer created in: T:\...\build\jpackage
```

---

## 🎯 Zusammenfassung

**Schnell-Build-Befehl:**
```cmd
gradlew clean shadowJar jpackage
```

**Ergebnis:** Vollständig funktionsfähige Windows-Anwendung mit benutzerdefiniertem Icon in `build\jpackage\MinewacheClient\`
