@echo off
echo ========================================
echo Minewache-Client Installer Test Script
echo ========================================

echo.
echo Dieses Script testet den erstellten Installer
echo und verifiziert alle Funktionen.
echo.

:: Prüfe ob Installer existiert
set "INSTALLER_PATH=build\installer\MinewacheClient-Installer-1.0.0.exe"

if not exist "%INSTALLER_PATH%" (
    echo [FEHLER] Installer nicht gefunden!
    echo Pfad: %INSTALLER_PATH%
    echo.
    echo Bitte erstellen Sie zuerst den Installer:
    echo   build-installer-complete.bat
    echo.
    pause
    exit /b 1
)

echo [OK] Installer gefunden: %INSTALLER_PATH%

:: Dateigröße anzeigen
for %%A in ("%INSTALLER_PATH%") do (
    set /a "size=%%~zA / 1024 / 1024"
    echo [INFO] Installer-Groesse: !size! MB
)

echo.

:: Prüfe Installer-Eigenschaften
echo Installer-Eigenschaften pruefen...
echo.

:: Zeige Datei-Eigenschaften
echo Datei-Details:
dir "%INSTALLER_PATH%" | findstr "MinewacheClient-Installer"

echo.

:: Test-Optionen anzeigen
echo ========================================
echo TEST-OPTIONEN
echo ========================================
echo.
echo 1. Installer starten (normale Installation)
echo 2. Installer im Silent-Modus testen
echo 3. Installer-Eigenschaften anzeigen
echo 4. Installer-Integrität pruefen
echo 5. Alle Tests beenden
echo.

:menu
set /p choice="Waehlen Sie eine Option (1-5): "

if "%choice%"=="1" goto test_normal
if "%choice%"=="2" goto test_silent
if "%choice%"=="3" goto show_properties
if "%choice%"=="4" goto check_integrity
if "%choice%"=="5" goto end

echo Ungueltige Auswahl. Bitte waehlen Sie 1-5.
goto menu

:test_normal
echo.
echo Starte Installer im normalen Modus...
echo.
echo HINWEIS: 
echo - Testen Sie alle Installationsoptionen
echo - Pruefen Sie Desktop-Verknuepfung
echo - Pruefen Sie Startmenue-Eintrag
echo - Testen Sie "Nach Installation starten"
echo.
start "" "%INSTALLER_PATH%"
echo.
echo Installer gestartet. Kehren Sie zu diesem Fenster zurueck,
echo wenn Sie weitere Tests durchfuehren moechten.
echo.
pause
goto menu

:test_silent
echo.
echo Starte Installer im Silent-Modus...
echo.
echo WARNUNG: Dies installiert die Anwendung automatisch
echo ohne Benutzerinteraktion!
echo.
set /p confirm="Fortfahren? (J/N): "
if /i not "%confirm%"=="J" goto menu

echo.
echo Fuehre Silent-Installation durch...
"%INSTALLER_PATH%" /SILENT /NORESTART

if %ERRORLEVEL% equ 0 (
    echo [OK] Silent-Installation erfolgreich
    echo.
    echo Pruefen Sie:
    echo - Programme und Features (Systemsteuerung)
    echo - Installationsverzeichnis
    echo - Startmenue-Eintraege
) else (
    echo [FEHLER] Silent-Installation fehlgeschlagen (Exit Code: %ERRORLEVEL%)
)

echo.
pause
goto menu

:show_properties
echo.
echo Installer-Eigenschaften:
echo.

:: Zeige erweiterte Datei-Informationen
powershell -Command "Get-ItemProperty '%INSTALLER_PATH%' | Select-Object Name, Length, CreationTime, LastWriteTime | Format-List"

echo.
echo Digitale Signatur pruefen...
powershell -Command "Get-AuthenticodeSignature '%INSTALLER_PATH%' | Select-Object Status, StatusMessage | Format-List"

echo.
pause
goto menu

:check_integrity
echo.
echo Installer-Integritaet pruefen...
echo.

:: Prüfe ob Datei beschädigt ist
echo Datei-Hash berechnen...
powershell -Command "Get-FileHash '%INSTALLER_PATH%' -Algorithm SHA256 | Select-Object Algorithm, Hash | Format-List"

echo.
echo Installer-Struktur pruefen...

:: Versuche Installer-Informationen zu extrahieren
powershell -Command "try { $info = [System.Diagnostics.FileVersionInfo]::GetVersionInfo('%INSTALLER_PATH%'); Write-Host 'Datei-Version:' $info.FileVersion; Write-Host 'Produkt-Version:' $info.ProductVersion; Write-Host 'Beschreibung:' $info.FileDescription; Write-Host 'Firma:' $info.CompanyName } catch { Write-Host 'Keine Versionsinformationen verfuegbar' }"

echo.
pause
goto menu

:end
echo.
echo ========================================
echo TEST-ZUSAMMENFASSUNG
echo ========================================
echo.
echo Installer-Datei: %INSTALLER_PATH%
echo Status: Verfuegbar und testbereit
echo.
echo EMPFOHLENE TESTS:
echo.
echo 1. Normale Installation:
echo    - Alle Installationsoptionen testen
echo    - Desktop-Verknuepfung pruefen
echo    - Startmenue-Eintrag pruefen
echo    - Anwendung nach Installation starten
echo.
echo 2. Deinstallation:
echo    - Systemsteuerung > Programme und Features
echo    - Oder: Startmenue > Minewache Launcher > Deinstallieren
echo.
echo 3. Upgrade-Test:
echo    - Installer erneut ausfuehren
echo    - Upgrade-Verhalten pruefen
echo.
echo 4. Silent-Installation:
echo    - Fuer automatisierte Verteilung
echo    - Parameter: /SILENT /NORESTART
echo.
echo VERTEILUNG:
echo.
echo Der Installer ist bereit fuer die Verteilung!
echo Benutzer benoetigen nur diese eine Datei:
echo %INSTALLER_PATH%
echo.
echo Weitere Informationen:
echo - INSTALLER_GUIDE.md
echo - BUILD_GUIDE.md
echo.

pause
