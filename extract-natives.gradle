// Füge diese Datei zu deinem Projekt hinzu und führe sie mit ./gradlew -b extract-natives.gradle extractNatives aus

task extractNatives {
    doLast {
        def javafxVersion = "21.0.1" // Muss mit deiner JavaFX-Version übereinstimmen
        def nativesDir = new File("${buildDir}/javafx-natives")
        nativesDir.mkdirs()
        
        // Lade die JavaFX-Bibliotheken herunter
        configurations.create("javafxNatives")
        repositories {
            mavenCentral()
        }
        
        dependencies.add("javafxNatives", "org.openjfx:javafx-graphics:${javafxVersion}:win")
        
        // Extrahiere die nativen Bibliotheken
        configurations.getByName("javafxNatives").files.each { file ->
            if (file.name.contains("javafx-graphics")) {
                copy {
                    from zipTree(file)
                    into nativesDir
                    include "**/*.dll"
                }
            }
        }
        
        // Kopiere die nativen Bibliotheken in das Verzeichnis für das native Image
        copy {
            from nativesDir
            into "${buildDir}/native/nativeCompile"
            include "**/*.dll"
        }
        
        println "Native Bibliotheken wurden nach ${buildDir}/native/nativeCompile extrahiert"
    }
}