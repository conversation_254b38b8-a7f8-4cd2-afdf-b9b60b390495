# Minewache-Client Installer Guide

## Vollständige Anleitung zur Erstellung eines professionellen Windows-Installers

---

## 📋 Voraussetzungen

### Erforderliche Software:
- **Java Development Kit (JDK) 21** oder höher
- **Inno Setup 5 oder 6** (kost<PERSON><PERSON> von https://jrsoftware.org/isinfo.php)
- **Git** (für Versionskontrolle)
- **Windows 10/11** (für Windows-spezifische Builds)

### Inno Setup Installation:
1. Laden Sie Inno Setup von https://jrsoftware.org/isinfo.php herunter
2. Installieren Sie es mit Standardeinstellungen
3. Das Build-Script erkennt automatisch die Installation

---

## 🏗️ Installer-Build-Prozess

### Schnell-Build (Empfohlen):
```cmd
build-installer-complete.bat
```

### Manueller Build:
```cmd
gradlew clean shadowJar jpackage createInstaller
```

### Schritt-für-Schritt Build:
```cmd
# 1. Projekt bereinigen
gradlew clean

# 2. Shadow JAR erstellen
gradlew shadowJar

# 3. jpackage App-Image erstellen
gradlew jpackage

# 4. Inno Setup Installer erstellen
gradlew createInstaller
```

---

## 📁 Dateispeicherorte

### Generierte Dateien:
```
build/
├── libs/
│   └── Minewache-Client-1.0-SNAPSHOT.jar    # Shadow JAR
├── jpackage/
│   └── MinewacheClient/                      # App-Image für Installer
│       ├── MinewacheClient.exe               # Hauptanwendung
│       ├── app/                              # Anwendungsdateien
│       └── runtime/                          # Java Runtime
└── installer/
    └── MinewacheClient-Installer-1.0.0.exe  # FINALER INSTALLER
```

### Installer-Konfiguration:
```
installer/
├── minewache-installer.iss                  # Inno Setup Script
├── install-info.txt                         # Installations-Info
├── install-complete.txt                     # Abschluss-Info
└── (wizard-image.bmp)                       # Optional: Installer-Bilder
```

---

## 🎨 Installer-Features

### GUI-Installation mit:
- **Installationspfad-Auswahl** (Standard: Programme)
- **Desktop-Verknüpfung** (optional)
- **Startmenü-Eintrag** (optional)
- **Schnellstart-Verknüpfung** (optional, ältere Windows-Versionen)
- **Nach Installation starten** (optional)

### Automatische Funktionen:
- **Fortschrittsanzeige** während Installation
- **Upgrade-Erkennung** (deinstalliert alte Versionen)
- **Registry-Einträge** für "Programme hinzufügen/entfernen"
- **Professioneller Deinstaller**
- **Dateierweiterungen** (.minewache Dateien)

### Sicherheit:
- **Niedrigste Berechtigungen** (keine Admin-Rechte erforderlich)
- **64-bit Kompatibilität**
- **Moderne Komprimierung** (LZMA2)

---

## 📦 Verteilung

### Für Endbenutzer:
**Nur eine Datei erforderlich:**
```
MinewacheClient-Installer-1.0.0.exe
```

### Verteilungsvorteile:
- **Keine Java-Installation** erforderlich (Runtime eingebettet)
- **Keine zusätzlichen Downloads** während Installation
- **Professionelle Installation** mit GUI
- **Automatische Verknüpfungen**
- **Saubere Deinstallation**

### Benutzeranweisungen:
1. `MinewacheClient-Installer-1.0.0.exe` herunterladen
2. Installer ausführen (Doppelklick)
3. Installationsoptionen auswählen
4. Installation abwarten
5. Launcher starten und verwenden

---

## ⚙️ Installer-Konfiguration

### Wichtige Inno Setup Parameter:
```pascal
AppName=Minewache Launcher
AppVersion=1.0.0
AppPublisher=Die Minewache
DefaultDirName={autopf}\Minewache Launcher
PrivilegesRequired=lowest
Compression=lzma2/ultra64
```

### Anpassbare Optionen:
- **App-Name und Version** in `minewache-installer.iss`
- **Standard-Installationspfad**
- **Installer-Bilder** (wizard-image.bmp, wizard-small.bmp)
- **Lizenz-Text** (LICENSE.txt)
- **Installations-Texte** (install-info.txt, install-complete.txt)

---

## 🔧 Fehlerbehebung

### Problem: Inno Setup nicht gefunden
**Lösung:**
```cmd
# Installieren Sie Inno Setup von:
https://jrsoftware.org/isinfo.php

# Unterstützte Pfade:
C:\Program Files (x86)\Inno Setup 6\ISCC.exe
C:\Program Files\Inno Setup 6\ISCC.exe
C:\Program Files (x86)\Inno Setup 5\ISCC.exe
C:\Program Files\Inno Setup 5\ISCC.exe
```

### Problem: jpackage schlägt fehl
**Lösung:**
```cmd
# Java-Version prüfen
java -version

# Gradle Daemon neu starten
gradlew --stop
gradlew clean shadowJar jpackage
```

### Problem: Installer-Kompilierung schlägt fehl
**Lösung 1:** Script-Pfade prüfen
```cmd
# Prüfen Sie ob alle Dateien existieren:
installer\minewache-installer.iss
build\jpackage\MinewacheClient\
src\main\resources\icons\logo.ico
```

**Lösung 2:** Manuelle Kompilierung
```cmd
"C:\Program Files (x86)\Inno Setup 6\ISCC.exe" installer\minewache-installer.iss
```

### Problem: Icon wird nicht angezeigt
**Lösung:**
```cmd
# Icon-Datei erstellen:
src\main\resources\icons\logo.ico

# Anforderungen:
- Format: .ico
- Größen: 16x16, 32x32, 48x48, 256x256
- Farbtiefe: 32-bit mit Alpha-Kanal
```

---

## 🎯 Erweiterte Konfiguration

### Installer-Sprachen:
```pascal
[Languages]
Name: "german"; MessagesFile: "compiler:Languages\German.isl"
Name: "english"; MessagesFile: "compiler:Default.isl"
```

### Benutzerdefinierte Aktionen:
```pascal
[Run]
Filename: "{app}\MinewacheClient.exe"; 
Description: "Launcher starten"; 
Flags: nowait postinstall skipifsilent; 
Tasks: launchafter
```

### Registry-Einträge:
```pascal
[Registry]
Root: HKLM; 
Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\Minewache Launcher"; 
ValueType: string; 
ValueName: "DisplayName"; 
ValueData: "Minewache Launcher"
```

---

## ✅ Erfolgreiche Installer-Verifikation

### Checkliste:
- [ ] Build ohne Fehler abgeschlossen
- [ ] `MinewacheClient-Installer-1.0.0.exe` existiert
- [ ] Installer startet ohne Fehler
- [ ] Installation funktioniert korrekt
- [ ] Desktop-Verknüpfung wird erstellt (falls ausgewählt)
- [ ] Startmenü-Eintrag wird erstellt (falls ausgewählt)
- [ ] Anwendung startet nach Installation
- [ ] Deinstallation funktioniert korrekt

### Finale Ausgabe:
```
BUILD SUCCESSFUL
Installer created successfully!
Installer location: T:\...\build\installer\MinewacheClient-Installer-1.0.0.exe
File size: XX MB
```

---

## 🎯 Zusammenfassung

**Schnell-Build-Befehl:**
```cmd
build-installer-complete.bat
```

**Ergebnis:** 
Professioneller Windows-Installer (`MinewacheClient-Installer-1.0.0.exe`) mit:
- GUI-Installation
- Eingebetteter Java-Runtime
- Automatischen Verknüpfungen
- Sauberer Deinstallation
- Keine zusätzlichen Abhängigkeiten

**Verteilung:** Nur eine .exe-Datei für Endbenutzer erforderlich!
