@echo off
echo ========================================
echo Minewache-Client Build Script
echo ========================================

echo.
echo Schritt 1: Projekt bereinigen...
call gradlew clean
if %ERRORLEVEL% neq 0 (
    echo FEHLER: Clean fehlgeschlagen!
    pause
    exit /b 1
)

echo.
echo Schritt 2: Shadow JAR erstellen...
call gradlew shadowJar
if %ERRORLEVEL% neq 0 (
    echo FEHLER: Shadow JAR Build fehlgeschlagen!
    pause
    exit /b 1
)

echo.
echo Schritt 3: jpackage mit Icon erstellen...
call gradlew jpackage
if %ERRORLEVEL% neq 0 (
    echo FEHLER: jpackage Build fehlgeschlagen!
    pause
    exit /b 1
)

echo.
echo ========================================
echo BUILD ERFOLGREICH ABGESCHLOSSEN!
echo ========================================
echo.
echo Anwendung erstellt in: build\jpackage\MinewacheClient\
echo Hauptdatei: MinewacheClient.exe
echo.
echo Icon-Status pruefen:
if exist "src\main\resources\icons\logo.ico" (
    echo [OK] Icon-Datei gefunden: src\main\resources\icons\logo.ico
) else (
    echo [WARNUNG] Icon-Datei nicht gefunden!
)

echo.
echo Anwendung testen? (J/N)
set /p test="Eingabe: "
if /i "%test%"=="J" (
    echo Starte MinewacheClient...
    start "" "build\jpackage\MinewacheClient\MinewacheClient.exe"
)

echo.
echo Build-Anleitung: BUILD_GUIDE.md
pause
